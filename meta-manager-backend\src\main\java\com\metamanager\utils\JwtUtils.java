package com.metamanager.utils;

import com.metamanager.common.Constants;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 */
@Component
public class JwtUtils {

    // 令牌自定义标识
    @Value("${jwt.header}")
    private String header;

    // 令牌密钥
    @Value("${jwt.secret}")
    private String secret;

    // 令牌有效期（默认30分钟）
    @Value("${jwt.expireTime}")
    private int expireTime;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public String getUserKey(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, Constants.LOGIN_USER_KEY);
    }

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public String getUserId(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, Constants.JWT_USERID);
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public String getUserName(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, Constants.JWT_USERNAME);
    }

    /**
     * 获取用户头像
     *
     * @return 用户头像
     */
    public String getAvatar(String token) {
        Claims claims = parseToken(token);
        return getValue(claims, Constants.JWT_AVATAR);
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    public String getToken(javax.servlet.http.HttpServletRequest request) {
        String token = request.getHeader(header);
        if (token != null && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    private String getValue(Claims claims, String key) {
        return claims.get(key).toString();
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String createToken(Map<String, Object> claims) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes());
        return Jwts.builder()
                .setClaims(claims)
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes());
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 根据身份信息获取键值
     *
     * @param userKey 用户信息
     * @return 键值
     */
    public String getTokenKey(String userKey) {
        return Constants.LOGIN_USER_KEY + userKey;
    }

    /**
     * 生成令牌
     *
     * @param userKey 用户标识
     * @param userId 用户ID
     * @param username 用户名
     * @param avatar 用户头像
     * @return 令牌
     */
    public String generateToken(String userKey, String userId, String username, String avatar) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.LOGIN_USER_KEY, userKey);
        claims.put(Constants.JWT_USERID, userId);
        claims.put(Constants.JWT_USERNAME, username);
        claims.put(Constants.JWT_AVATAR, avatar);
        claims.put(Constants.JWT_CREATED, new Date());
        return createToken(claims);
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param token 令牌
     * @return 令牌
     */
    public void verifyToken(String token) {
        long expireTime = this.expireTime * MILLIS_MINUTE;
        Claims claims = parseToken(token);
        Date created = claims.get(Constants.JWT_CREATED, Date.class);
        Date now = new Date();
        if ((now.getTime() - created.getTime()) > (expireTime - MILLIS_MINUTE_TEN)) {
            // 自动刷新Redis缓存
            String userKey = getUserKey(token);
            // 这里可以刷新Redis中的用户信息缓存
        }
    }
}
