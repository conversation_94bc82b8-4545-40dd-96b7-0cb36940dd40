package com.metamanager.service.impl;

import com.metamanager.service.LoginService;
import org.springframework.stereotype.Service;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Service
public class LoginServiceImpl implements LoginService {

    /**
     * 登录验证
     * 
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    @Override
    public String login(String username, String password, String code, String uuid) {
        // 简单的用户名密码验证
        if ("admin".equals(username) && "admin123".equals(password)) {
            // 生成简单的token（实际项目中应该使用JWT）
            return "mock-jwt-token-" + System.currentTimeMillis();
        }
        
        throw new RuntimeException("用户名或密码错误");
    }
}
