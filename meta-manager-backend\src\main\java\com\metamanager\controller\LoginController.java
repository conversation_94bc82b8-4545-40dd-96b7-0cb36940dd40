package com.metamanager.controller;

import com.metamanager.common.Result;
import com.metamanager.dto.LoginRequest;
import com.metamanager.dto.LoginResponse;
import com.metamanager.service.LoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class LoginController {

    @Autowired
    private LoginService loginService;

    /**
     * 登录方法
     * 
     * @param loginRequest 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody @Valid LoginRequest loginRequest) {
        // 生成令牌
        String token = loginService.login(loginRequest.getUsername(), loginRequest.getPassword(), 
                                        loginRequest.getCode(), loginRequest.getUuid());
        
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        
        return Result.ok(response);
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public Result<Map<String, Object>> getInfo() {
        Map<String, Object> data = new HashMap<>();
        
        // 模拟用户信息
        Map<String, Object> user = new HashMap<>();
        user.put("userId", 1L);
        user.put("userName", "admin");
        user.put("nickName", "管理员");
        user.put("email", "<EMAIL>");
        user.put("phonenumber", "15888888888");
        user.put("sex", "0");
        user.put("avatar", "");
        
        data.put("user", user);
        data.put("roles", new String[]{"admin"});
        data.put("permissions", new String[]{"*:*:*"});
        
        return Result.ok(data);
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        return Result.ok();
    }

    /**
     * 获取验证码
     */
    @GetMapping("/captchaImage")
    public Result<Map<String, Object>> getCode() {
        Map<String, Object> data = new HashMap<>();
        data.put("captchaEnabled", false);
        data.put("uuid", "");
        data.put("img", "");
        
        return Result.ok(data);
    }
}
