import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      hidden: true
    }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '首页',
          icon: 'House',
          affix: true
        }
      }
    ]
  },
  {
    path: '/system',
    component: () => import('@/layout/index.vue'),
    redirect: '/system/user',
    meta: {
      title: '系统管理',
      icon: 'Setting'
    },
    children: [
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/system/user/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'User'
        }
      },
      {
        path: 'role',
        name: 'Role',
        component: () => import('@/views/system/role/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'UserFilled'
        }
      },
      {
        path: 'menu',
        name: 'Menu',
        component: () => import('@/views/system/menu/index.vue'),
        meta: {
          title: '菜单管理',
          icon: 'Menu'
        }
      }
    ]
  },
  {
    path: '/meta',
    component: () => import('@/layout/index.vue'),
    redirect: '/meta/system',
    meta: {
      title: '元数据管理',
      icon: 'DataBoard'
    },
    children: [
      {
        path: 'system',
        name: 'MetaSystem',
        component: () => import('@/views/meta/system/index.vue'),
        meta: {
          title: '系统管理',
          icon: 'Monitor'
        }
      },
      {
        path: 'table',
        name: 'MetaTable',
        component: () => import('@/views/meta/table/index.vue'),
        meta: {
          title: '数据表管理',
          icon: 'Grid'
        }
      },
      {
        path: 'data',
        name: 'MetaData',
        component: () => import('@/views/meta/data/index.vue'),
        meta: {
          title: '数据维护',
          icon: 'Edit'
        }
      }
    ]
  },
  {
    path: '/approval',
    component: () => import('@/layout/index.vue'),
    redirect: '/approval/list',
    meta: {
      title: '审批管理',
      icon: 'Document'
    },
    children: [
      {
        path: 'list',
        name: 'ApprovalList',
        component: () => import('@/views/approval/list/index.vue'),
        meta: {
          title: '审批列表',
          icon: 'DocumentChecked'
        }
      },
      {
        path: 'config',
        name: 'ApprovalConfig',
        component: () => import('@/views/approval/config/index.vue'),
        meta: {
          title: '审批配置',
          icon: 'Tools'
        }
      }
    ]
  },
  {
    path: '/log',
    component: () => import('@/layout/index.vue'),
    redirect: '/log/operation',
    meta: {
      title: '日志管理',
      icon: 'Document'
    },
    children: [
      {
        path: 'operation',
        name: 'OperationLog',
        component: () => import('@/views/log/operation/index.vue'),
        meta: {
          title: '操作日志',
          icon: 'DocumentCopy'
        }
      }
    ]
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '404',
      hidden: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    meta: {
      hidden: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const token = getToken()

  if (token) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      if (userStore.name) {
        next()
      } else {
        try {
          await userStore.getInfo()
          next()
        } catch (error) {
          await userStore.logout()
          next(`/login?redirect=${to.path}`)
        }
      }
    }
  } else {
    if (to.path === '/login') {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
    }
  }
})

export default router
