-- ----------------------------
-- 基础数据配置管理平台数据库初始化脚本
-- ----------------------------

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `meta_manager` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE `meta_manager`;

-- ----------------------------
-- 1、用户信息表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `user_id`           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '用户ID',
  `user_name`         varchar(30)     NOT NULL                   COMMENT '用户账号',
  `nick_name`         varchar(30)     NOT NULL                   COMMENT '用户昵称',
  `email`             varchar(50)     DEFAULT ''                 COMMENT '用户邮箱',
  `phonenumber`       varchar(11)     DEFAULT ''                 COMMENT '手机号码',
  `sex`               char(1)         DEFAULT '0'                COMMENT '用户性别（0男 1女 2未知）',
  `avatar`            varchar(100)    DEFAULT ''                 COMMENT '头像地址',
  `password`          varchar(100)    DEFAULT ''                 COMMENT '密码',
  `status`            char(1)         DEFAULT '0'                COMMENT '帐号状态（0正常 1停用）',
  `login_ip`          varchar(128)    DEFAULT ''                 COMMENT '最后登录IP',
  `login_date`        datetime                                   COMMENT '最后登录时间',
  `create_by`         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  `create_time`       datetime                                   COMMENT '创建时间',
  `update_by`         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  `update_time`       datetime                                   COMMENT '更新时间',
  `remark`            varchar(500)    DEFAULT NULL               COMMENT '备注',
  `deleted`           char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '用户信息表';

-- ----------------------------
-- 2、角色信息表
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `role_id`              bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '角色ID',
  `role_name`            varchar(30)     NOT NULL                   COMMENT '角色名称',
  `role_key`             varchar(100)    NOT NULL                   COMMENT '角色权限字符串',
  `role_sort`            int(4)          NOT NULL                   COMMENT '显示顺序',
  `status`               char(1)         NOT NULL                   COMMENT '角色状态（0正常 1停用）',
  `create_by`            varchar(64)     DEFAULT ''                 COMMENT '创建者',
  `create_time`          datetime                                   COMMENT '创建时间',
  `update_by`            varchar(64)     DEFAULT ''                 COMMENT '更新者',
  `update_time`          datetime                                   COMMENT '更新时间',
  `remark`               varchar(500)    DEFAULT NULL               COMMENT '备注',
  `deleted`              char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '角色信息表';

-- ----------------------------
-- 3、菜单权限表
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `menu_id`         bigint(20)     NOT NULL AUTO_INCREMENT    COMMENT '菜单ID',
  `menu_name`       varchar(50)    NOT NULL                   COMMENT '菜单名称',
  `parent_id`       bigint(20)     DEFAULT 0                  COMMENT '父菜单ID',
  `order_num`       int(4)         DEFAULT 0                  COMMENT '显示顺序',
  `path`            varchar(200)   DEFAULT ''                 COMMENT '路由地址',
  `component`       varchar(255)   DEFAULT NULL               COMMENT '组件路径',
  `query`           varchar(255)   DEFAULT NULL               COMMENT '路由参数',
  `is_frame`        int(1)         DEFAULT 1                  COMMENT '是否为外链（0是 1否）',
  `is_cache`        int(1)         DEFAULT 0                  COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type`       char(1)        DEFAULT ''                 COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible`         char(1)        DEFAULT 0                  COMMENT '菜单状态（0显示 1隐藏）',
  `status`          char(1)        DEFAULT 0                  COMMENT '菜单状态（0正常 1停用）',
  `perms`           varchar(100)   DEFAULT NULL               COMMENT '权限标识',
  `icon`            varchar(100)   DEFAULT '#'                COMMENT '菜单图标',
  `create_by`       varchar(64)    DEFAULT ''                 COMMENT '创建者',
  `create_time`     datetime                                  COMMENT '创建时间',
  `update_by`       varchar(64)    DEFAULT ''                 COMMENT '更新者',
  `update_time`     datetime                                  COMMENT '更新时间',
  `remark`          varchar(500)   DEFAULT ''                 COMMENT '备注',
  `deleted`         char(1)        DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2000 DEFAULT CHARSET=utf8mb4 COMMENT = '菜单权限表';

-- ----------------------------
-- 4、用户和角色关联表  用户N-1角色
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `user_id`   bigint(20) NOT NULL COMMENT '用户ID',
  `role_id`   bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY(`user_id`, `role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '用户和角色关联表';

-- ----------------------------
-- 5、角色和菜单关联表  角色1-N菜单
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
  `role_id`   bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id`   bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY(`role_id`, `menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '角色和菜单关联表';

-- ----------------------------
-- 6、系统管理表
-- ----------------------------
DROP TABLE IF EXISTS `meta_system`;
CREATE TABLE `meta_system` (
  `system_id`           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '系统ID',
  `system_name`         varchar(50)     NOT NULL                   COMMENT '系统名称',
  `system_code`         varchar(50)     NOT NULL                   COMMENT '系统编码',
  `database_type`       varchar(20)     NOT NULL                   COMMENT '数据库类型',
  `database_host`       varchar(100)    NOT NULL                   COMMENT '数据库主机',
  `database_port`       int(6)          NOT NULL                   COMMENT '数据库端口',
  `database_name`       varchar(50)     NOT NULL                   COMMENT '数据库名称',
  `database_username`   varchar(50)     NOT NULL                   COMMENT '数据库用户名',
  `database_password`   varchar(200)    NOT NULL                   COMMENT '数据库密码',
  `status`              char(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
  `create_by`           varchar(64)     DEFAULT ''                 COMMENT '创建者',
  `create_time`         datetime                                   COMMENT '创建时间',
  `update_by`           varchar(64)     DEFAULT ''                 COMMENT '更新者',
  `update_time`         datetime                                   COMMENT '更新时间',
  `remark`              varchar(500)    DEFAULT NULL               COMMENT '备注',
  `deleted`             char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`system_id`),
  UNIQUE KEY `uk_system_code` (`system_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '系统管理表';

-- ----------------------------
-- 7、数据表配置表
-- ----------------------------
DROP TABLE IF EXISTS `meta_table`;
CREATE TABLE `meta_table` (
  `table_id`            bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '表ID',
  `system_id`           bigint(20)      NOT NULL                   COMMENT '系统ID',
  `table_name`          varchar(50)     NOT NULL                   COMMENT '表名',
  `table_comment`       varchar(200)    DEFAULT ''                 COMMENT '表注释',
  `table_type`          varchar(20)     DEFAULT 'table'            COMMENT '表类型（table表 view视图）',
  `engine`              varchar(20)     DEFAULT 'InnoDB'           COMMENT '存储引擎',
  `charset`             varchar(20)     DEFAULT 'utf8mb4'          COMMENT '字符集',
  `is_editable`         char(1)         DEFAULT '1'                COMMENT '是否可编辑（0否 1是）',
  `need_approval`       char(1)         DEFAULT '0'                COMMENT '是否需要审批（0否 1是）',
  `status`              char(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
  `create_by`           varchar(64)     DEFAULT ''                 COMMENT '创建者',
  `create_time`         datetime                                   COMMENT '创建时间',
  `update_by`           varchar(64)     DEFAULT ''                 COMMENT '更新者',
  `update_time`         datetime                                   COMMENT '更新时间',
  `remark`              varchar(500)    DEFAULT NULL               COMMENT '备注',
  `deleted`             char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`table_id`),
  UNIQUE KEY `uk_system_table` (`system_id`, `table_name`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '数据表配置表';

-- ----------------------------
-- 8、字段配置表
-- ----------------------------
DROP TABLE IF EXISTS `meta_column`;
CREATE TABLE `meta_column` (
  `column_id`           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '字段ID',
  `table_id`            bigint(20)      NOT NULL                   COMMENT '表ID',
  `column_name`         varchar(50)     NOT NULL                   COMMENT '字段名',
  `column_comment`      varchar(200)    DEFAULT ''                 COMMENT '字段注释',
  `column_type`         varchar(50)     NOT NULL                   COMMENT '字段类型',
  `column_length`       int(11)         DEFAULT NULL               COMMENT '字段长度',
  `is_nullable`         char(1)         DEFAULT '1'                COMMENT '是否可空（0否 1是）',
  `is_primary`          char(1)         DEFAULT '0'                COMMENT '是否主键（0否 1是）',
  `is_auto_increment`   char(1)         DEFAULT '0'                COMMENT '是否自增（0否 1是）',
  `default_value`       varchar(200)    DEFAULT NULL               COMMENT '默认值',
  `is_editable`         char(1)         DEFAULT '1'                COMMENT '是否可编辑（0否 1是）',
  `is_required`         char(1)         DEFAULT '0'                COMMENT '是否必填（0否 1是）',
  `display_name`        varchar(100)    DEFAULT ''                 COMMENT '显示名称',
  `display_order`       int(4)          DEFAULT 0                  COMMENT '显示顺序',
  `form_type`           varchar(20)     DEFAULT 'input'            COMMENT '表单类型',
  `dict_type`           varchar(100)    DEFAULT ''                 COMMENT '字典类型',
  `validation_rules`    varchar(500)    DEFAULT ''                 COMMENT '验证规则',
  `create_by`           varchar(64)     DEFAULT ''                 COMMENT '创建者',
  `create_time`         datetime                                   COMMENT '创建时间',
  `update_by`           varchar(64)     DEFAULT ''                 COMMENT '更新者',
  `update_time`         datetime                                   COMMENT '更新时间',
  `remark`              varchar(500)    DEFAULT NULL               COMMENT '备注',
  `deleted`             char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`column_id`),
  UNIQUE KEY `uk_table_column` (`table_id`, `column_name`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '字段配置表';

-- ----------------------------
-- 9、操作日志表
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log` (
  `oper_id`           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '日志主键',
  `title`             varchar(50)     DEFAULT ''                 COMMENT '模块标题',
  `business_type`     int(2)          DEFAULT 0                  COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method`            varchar(100)    DEFAULT ''                 COMMENT '方法名称',
  `request_method`    varchar(10)     DEFAULT ''                 COMMENT '请求方式',
  `operator_type`     int(1)          DEFAULT 0                  COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name`         varchar(50)     DEFAULT ''                 COMMENT '操作人员',
  `dept_name`         varchar(50)     DEFAULT ''                 COMMENT '部门名称',
  `oper_url`          varchar(255)    DEFAULT ''                 COMMENT '请求URL',
  `oper_ip`           varchar(128)    DEFAULT ''                 COMMENT '主机地址',
  `oper_location`     varchar(255)    DEFAULT ''                 COMMENT '操作地点',
  `oper_param`        varchar(2000)   DEFAULT ''                 COMMENT '请求参数',
  `json_result`       varchar(2000)   DEFAULT ''                 COMMENT '返回参数',
  `status`            int(1)          DEFAULT 0                  COMMENT '操作状态（0正常 1异常）',
  `error_msg`         varchar(2000)   DEFAULT ''                 COMMENT '错误消息',
  `oper_time`         datetime                                   COMMENT '操作时间',
  PRIMARY KEY (`oper_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '操作日志记录';

-- ----------------------------
-- 初始化数据
-- ----------------------------

-- 初始化用户信息表数据
INSERT INTO sys_user VALUES(1, 'admin', '管理员', '<EMAIL>', '15888888888', '1', '', 'admin123', '0', '127.0.0.1', sysdate(), 'admin', sysdate(), '', sysdate(), '管理员', '0');

-- 初始化角色信息表数据
INSERT INTO sys_role VALUES(1, '超级管理员', 'admin', 1, '0', 'admin', sysdate(), '', sysdate(), '超级管理员', '0');
INSERT INTO sys_role VALUES(2, '普通角色', 'common', 2, '0', 'admin', sysdate(), '', sysdate(), '普通角色', '0');

-- 初始化菜单信息表数据
INSERT INTO sys_menu VALUES(1, '系统管理', 0, 1, 'system', NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'admin', sysdate(), '', sysdate(), '系统管理目录', '0');
INSERT INTO sys_menu VALUES(2, '系统监控', 0, 2, 'monitor', NULL, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', sysdate(), '', sysdate(), '系统监控目录', '0');
INSERT INTO sys_menu VALUES(3, '系统工具', 0, 3, 'tool', NULL, '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', sysdate(), '', sysdate(), '系统工具目录', '0');

INSERT INTO sys_menu VALUES(100, '用户管理', 1, 1, 'user', 'system/user/index', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', sysdate(), '', sysdate(), '用户管理菜单', '0');
INSERT INTO sys_menu VALUES(101, '角色管理', 1, 2, 'role', 'system/role/index', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', sysdate(), '', sysdate(), '角色管理菜单', '0');
INSERT INTO sys_menu VALUES(102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', sysdate(), '', sysdate(), '菜单管理菜单', '0');

-- 初始化用户和角色关联表数据
INSERT INTO sys_user_role VALUES ('1', '1');

-- 初始化角色和菜单关联表数据
INSERT INTO sys_role_menu VALUES ('1', '1');
INSERT INTO sys_role_menu VALUES ('1', '2');
INSERT INTO sys_role_menu VALUES ('1', '3');
INSERT INTO sys_role_menu VALUES ('1', '100');
INSERT INTO sys_role_menu VALUES ('1', '101');
INSERT INTO sys_role_menu VALUES ('1', '102');
