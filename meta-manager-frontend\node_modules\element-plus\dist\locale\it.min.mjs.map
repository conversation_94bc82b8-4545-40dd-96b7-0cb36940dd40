{"version": 3, "file": "it.min.mjs", "sources": ["../../../../packages/locale/lang/it.ts"], "sourcesContent": ["export default {\n  name: 'it',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON>gg<PERSON>',\n      cancel: '<PERSON><PERSON>a',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Seleziona data',\n      selectTime: 'Seleziona ora',\n      startDate: 'Data inizio',\n      startTime: 'Ora inizio',\n      endDate: 'Data fine',\n      endTime: 'Ora fine',\n      prevYear: 'Anno precedente',\n      nextYear: 'Anno successivo',\n      prevMonth: 'Mese precedente',\n      nextMonth: 'Mese successivo',\n      year: '',\n      month1: 'Gennaio',\n      month2: 'Febbraio',\n      month3: 'Marzo',\n      month4: 'Aprile',\n      month5: 'Maggio',\n      month6: 'Giugno',\n      month7: 'Luglio',\n      month8: 'Agosto',\n      month9: 'Settembre',\n      month10: '<PERSON><PERSON>',\n      month11: 'Novembre',\n      month12: 'Di<PERSON><PERSON>',\n      // week: 'setti<PERSON>',\n      weeks: {\n        sun: 'Dom',\n        mon: 'Lun',\n        tue: 'Mar',\n        wed: 'Mer',\n        thu: 'G<PERSON>',\n        fri: 'Ven',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Gen',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mag',\n        jun: 'Giu',\n        jul: 'Lug',\n        aug: 'Ago',\n        sep: 'Set',\n        oct: 'Ott',\n        nov: 'Nov',\n        dec: 'Dic',\n      },\n    },\n    select: {\n      loading: 'Caricamento',\n      noMatch: 'Nessuna corrispondenza',\n      noData: 'Nessun dato',\n      placeholder: 'Seleziona',\n    },\n    mention: {\n      loading: 'Caricamento',\n    },\n    cascader: {\n      noMatch: 'Nessuna corrispondenza',\n      loading: 'Caricamento',\n      placeholder: 'Seleziona',\n      noData: 'Nessun dato',\n    },\n    pagination: {\n      goto: 'Vai a',\n      pagesize: '/page',\n      total: 'Totale {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'OK',\n      cancel: 'Cancella',\n      error: 'Input non valido',\n    },\n    upload: {\n      deleteTip: 'Premi cancella per rimuovere',\n      delete: 'Cancella',\n      preview: 'Anteprima',\n      continue: 'Continua',\n    },\n    table: {\n      emptyText: 'Nessun dato',\n      confirmFilter: 'Conferma',\n      resetFilter: 'Reset',\n      clearFilter: 'Tutti',\n      sumText: 'Somma',\n    },\n    tree: {\n      emptyText: 'Nessun dato',\n    },\n    transfer: {\n      noMatch: 'Nessuna corrispondenza',\n      noData: 'Nessun dato',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Inserisci filtro',\n      noCheckedFormat: '{total} elementi',\n      hasCheckedFormat: '{checked}/{total} selezionati',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}