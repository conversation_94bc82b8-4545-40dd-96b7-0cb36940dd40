<template>
  <div class="dashboard">
    <div class="page-container">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="welcome-card">
            <template #header>
              <div class="card-header">
                <span>欢迎使用基础数据配置管理平台</span>
              </div>
            </template>
            <div class="welcome-content">
              <h2>MetaManager</h2>
              <p>简化基础数据和配置表的维护工作，提高数据维护效率</p>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon color="#409EFF" size="40">
                  <Monitor />
                </el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ systemCount }}</div>
                <div class="stat-label">系统数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon color="#67C23A" size="40">
                  <Grid />
                </el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ tableCount }}</div>
                <div class="stat-label">数据表数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon color="#E6A23C" size="40">
                  <User />
                </el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ userCount }}</div>
                <div class="stat-label">用户数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon color="#F56C6C" size="40">
                  <DocumentCopy />
                </el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ logCount }}</div>
                <div class="stat-label">今日操作</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>快速入口</span>
              </div>
            </template>
            <div class="quick-links">
              <el-button type="primary" @click="$router.push('/meta/system')">
                <el-icon><Monitor /></el-icon>
                系统管理
              </el-button>
              <el-button type="success" @click="$router.push('/meta/table')">
                <el-icon><Grid /></el-icon>
                数据表管理
              </el-button>
              <el-button type="warning" @click="$router.push('/meta/data')">
                <el-icon><Edit /></el-icon>
                数据维护
              </el-button>
              <el-button type="info" @click="$router.push('/log/operation')">
                <el-icon><DocumentCopy /></el-icon>
                操作日志
              </el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>系统信息</span>
              </div>
            </template>
            <div class="system-info">
              <div class="info-item">
                <span class="info-label">系统版本：</span>
                <span class="info-value">v1.0.0</span>
              </div>
              <div class="info-item">
                <span class="info-label">运行环境：</span>
                <span class="info-value">生产环境</span>
              </div>
              <div class="info-item">
                <span class="info-label">数据库：</span>
                <span class="info-value">MySQL 8.0</span>
              </div>
              <div class="info-item">
                <span class="info-label">服务器：</span>
                <span class="info-value">Spring Boot 2.7.14</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
// 模拟数据
const systemCount = ref(5)
const tableCount = ref(23)
const userCount = ref(12)
const logCount = ref(156)

onMounted(() => {
  // 这里可以调用API获取真实数据
  console.log('Dashboard mounted')
})
</script>

<style lang="scss" scoped>
.dashboard {
  .welcome-card {
    .welcome-content {
      text-align: center;
      padding: 20px 0;
      
      h2 {
        color: #409EFF;
        margin-bottom: 10px;
        font-size: 28px;
      }
      
      p {
        color: #666;
        font-size: 16px;
      }
    }
  }

  .stat-card {
    .stat-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      
      .stat-icon {
        margin-right: 15px;
      }
      
      .stat-content {
        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .quick-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    
    .el-button {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .el-icon {
        margin-right: 8px;
      }
    }
  }

  .system-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .info-label {
        color: #909399;
      }
      
      .info-value {
        color: #303133;
        font-weight: 500;
      }
    }
  }
}
</style>
