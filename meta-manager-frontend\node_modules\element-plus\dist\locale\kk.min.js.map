{"version": 3, "file": "kk.min.js", "sources": ["../../../../packages/locale/lang/kk.ts"], "sourcesContent": ["export default {\n  name: 'kk',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Қабылдау',\n      clear: 'Тазалау',\n    },\n    datepicker: {\n      now: 'Қазір',\n      today: 'Бүгін',\n      cancel: 'Болдырмау',\n      clear: 'Тазалау',\n      confirm: 'Қабылдау',\n      selectDate: 'Күнді таңдаңыз',\n      selectTime: 'Сағатты таңдаңыз',\n      startDate: 'Басталу күні',\n      startTime: 'Басталу сағаты',\n      endDate: 'Аяқталу күні',\n      endTime: 'Аяқталу сағаты',\n      prevYear: 'Алдыңғы жыл',\n      nextYear: 'Келесі жыл',\n      prevMonth: 'Алдыңғы ай',\n      nextMonth: 'Келесі ай',\n      year: 'Жыл',\n      month1: 'Қаңтар',\n      month2: 'Ақпан',\n      month3: 'Наурыз',\n      month4: 'Сәуір',\n      month5: 'Мамыр',\n      month6: 'Маусым',\n      month7: 'Шілде',\n      month8: 'Тамыз',\n      month9: 'Қыркүйек',\n      month10: 'Қазан',\n      month11: 'Қараша',\n      month12: 'Желтоқсан',\n      week: 'Апта',\n      weeks: {\n        sun: 'Жек',\n        mon: 'Дүй',\n        tue: 'Сей',\n        wed: 'Сәр',\n        thu: 'Бей',\n        fri: 'Жұм',\n        sat: 'Сен',\n      },\n      months: {\n        jan: 'Қаң',\n        feb: 'Ақп',\n        mar: 'Нау',\n        apr: 'Сәу',\n        may: 'Мам',\n        jun: 'Мау',\n        jul: 'Шіл',\n        aug: 'Там',\n        sep: 'Қыр',\n        oct: 'Қаз',\n        nov: 'Қар',\n        dec: 'Жел',\n      },\n    },\n    select: {\n      loading: 'Жүктелуде',\n      noMatch: 'Сәйкес деректер жоқ',\n      noData: 'Деректер жоқ',\n      placeholder: 'Таңдаңыз',\n    },\n    mention: {\n      loading: 'Жүктелуде',\n    },\n    cascader: {\n      noMatch: 'Сәйкес деректер жоқ',\n      loading: 'Жүктелуде',\n      placeholder: 'Таңдаңыз',\n      noData: 'Деректер жоқ',\n    },\n    pagination: {\n      goto: 'Бару',\n      pagesize: '/page',\n      total: 'Барлығы {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Хабар',\n      confirm: 'Қабылдау',\n      cancel: 'Болдырмау',\n      error: 'Жарамсыз енгізулер',\n    },\n    upload: {\n      deleteTip: 'Өшіруді басып өшіріңіз',\n      delete: 'Өшіру',\n      preview: 'Алдын ала қарау',\n      continue: 'Жалғастыру',\n    },\n    table: {\n      emptyText: 'Деректер жоқ',\n      confirmFilter: 'Қабылдау',\n      resetFilter: 'Қалпына келтіру',\n      clearFilter: 'Барлығы',\n      sumText: 'Сомасы',\n    },\n    tree: {\n      emptyText: 'Деректер жоқ',\n    },\n    transfer: {\n      noMatch: 'Сәйкес деректер жоқ',\n      noData: 'Деректер жоқ',\n      titles: ['List 1', 'List 2'],\n      filterPlaceholder: 'Кілт сөзді енгізіңіз',\n      noCheckedFormat: '{total} элэмэнт',\n      hasCheckedFormat: '{checked}/{total} құсбелгісі қойылды',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,gCAAgC,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,wDAAwD,CAAC,KAAK,CAAC,4CAA4C,CAAC,OAAO,CAAC,kDAAkD,CAAC,UAAU,CAAC,iFAAiF,CAAC,UAAU,CAAC,6FAA6F,CAAC,SAAS,CAAC,qEAAqE,CAAC,SAAS,CAAC,iFAAiF,CAAC,OAAO,CAAC,qEAAqE,CAAC,OAAO,CAAC,iFAAiF,CAAC,QAAQ,CAAC,+DAA+D,CAAC,QAAQ,CAAC,yDAAyD,CAAC,SAAS,CAAC,yDAAyD,CAAC,SAAS,CAAC,mDAAmD,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,kDAAkD,CAAC,OAAO,CAAC,gCAAgC,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,wDAAwD,CAAC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,0GAA0G,CAAC,MAAM,CAAC,qEAAqE,CAAC,WAAW,CAAC,kDAAkD,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0GAA0G,CAAC,OAAO,CAAC,wDAAwD,CAAC,WAAW,CAAC,kDAAkD,CAAC,MAAM,CAAC,qEAAqE,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,oDAAoD,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,kDAAkD,CAAC,MAAM,CAAC,wDAAwD,CAAC,KAAK,CAAC,yGAAyG,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,4HAA4H,CAAC,MAAM,CAAC,gCAAgC,CAAC,OAAO,CAAC,kFAAkF,CAAC,QAAQ,CAAC,8DAA8D,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qEAAqE,CAAC,aAAa,CAAC,kDAAkD,CAAC,WAAW,CAAC,uFAAuF,CAAC,WAAW,CAAC,4CAA4C,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qEAAqE,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0GAA0G,CAAC,MAAM,CAAC,qEAAqE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,gHAAgH,CAAC,eAAe,CAAC,oDAAoD,CAAC,gBAAgB,CAAC,2HAA2H,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}