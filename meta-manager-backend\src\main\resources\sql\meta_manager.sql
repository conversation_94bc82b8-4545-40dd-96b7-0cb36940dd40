-- ----------------------------
-- 基础数据配置管理平台数据库初始化脚本
-- ----------------------------

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `meta_manager` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE `meta_manager`;

-- ----------------------------
-- 1、用户信息表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `user_id`           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '用户ID',
  `user_name`         varchar(30)     NOT NULL                   COMMENT '用户账号',
  `nick_name`         varchar(30)     NOT NULL                   COMMENT '用户昵称',
  `email`             varchar(50)     DEFAULT ''                 COMMENT '用户邮箱',
  `phonenumber`       varchar(11)     DEFAULT ''                 COMMENT '手机号码',
  `sex`               char(1)         DEFAULT '0'                COMMENT '用户性别（0男 1女 2未知）',
  `avatar`            varchar(100)    DEFAULT ''                 COMMENT '头像地址',
  `password`          varchar(100)    DEFAULT ''                 COMMENT '密码',
  `status`            char(1)         DEFAULT '0'                COMMENT '帐号状态（0正常 1停用）',
  `login_ip`          varchar(128)    DEFAULT ''                 COMMENT '最后登录IP',
  `login_date`        datetime                                   COMMENT '最后登录时间',
  `create_by`         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  `create_time`       datetime                                   COMMENT '创建时间',
  `update_by`         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  `update_time`       datetime                                   COMMENT '更新时间',
  `remark`            varchar(500)    DEFAULT NULL               COMMENT '备注',
  `deleted`           char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '用户信息表';

-- ----------------------------
-- 2、角色信息表
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `role_id`              bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '角色ID',
  `role_name`            varchar(30)     NOT NULL                   COMMENT '角色名称',
  `role_key`             varchar(100)    NOT NULL                   COMMENT '角色权限字符串',
  `role_sort`            int(4)          NOT NULL                   COMMENT '显示顺序',
  `status`               char(1)         NOT NULL                   COMMENT '角色状态（0正常 1停用）',
  `create_by`            varchar(64)     DEFAULT ''                 COMMENT '创建者',
  `create_time`          datetime                                   COMMENT '创建时间',
  `update_by`            varchar(64)     DEFAULT ''                 COMMENT '更新者',
  `update_time`          datetime                                   COMMENT '更新时间',
  `remark`               varchar(500)    DEFAULT NULL               COMMENT '备注',
  `deleted`              char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '角色信息表';

-- ----------------------------
-- 3、菜单权限表
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `menu_id`         bigint(20)     NOT NULL AUTO_INCREMENT    COMMENT '菜单ID',
  `menu_name`       varchar(50)    NOT NULL                   COMMENT '菜单名称',
  `parent_id`       bigint(20)     DEFAULT 0                  COMMENT '父菜单ID',
  `order_num`       int(4)         DEFAULT 0                  COMMENT '显示顺序',
  `path`            varchar(200)   DEFAULT ''                 COMMENT '路由地址',
  `component`       varchar(255)   DEFAULT NULL               COMMENT '组件路径',
  `query`           varchar(255)   DEFAULT NULL               COMMENT '路由参数',
  `is_frame`        int(1)         DEFAULT 1                  COMMENT '是否为外链（0是 1否）',
  `is_cache`        int(1)         DEFAULT 0                  COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type`       char(1)        DEFAULT ''                 COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible`         char(1)        DEFAULT 0                  COMMENT '菜单状态（0显示 1隐藏）',
  `status`          char(1)        DEFAULT 0                  COMMENT '菜单状态（0正常 1停用）',
  `perms`           varchar(100)   DEFAULT NULL               COMMENT '权限标识',
  `icon`            varchar(100)   DEFAULT '#'                COMMENT '菜单图标',
  `create_by`       varchar(64)    DEFAULT ''                 COMMENT '创建者',
  `create_time`     datetime                                  COMMENT '创建时间',
  `update_by`       varchar(64)    DEFAULT ''                 COMMENT '更新者',
  `update_time`     datetime                                  COMMENT '更新时间',
  `remark`          varchar(500)   DEFAULT ''                 COMMENT '备注',
  `deleted`         char(1)        DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2000 DEFAULT CHARSET=utf8mb4 COMMENT = '菜单权限表';

-- ----------------------------
-- 4、用户和角色关联表  用户N-1角色
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `user_id`   bigint(20) NOT NULL COMMENT '用户ID',
  `role_id`   bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY(`user_id`, `role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '用户和角色关联表';

-- ----------------------------
-- 5、角色和菜单关联表  角色1-N菜单
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
  `role_id`   bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id`   bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY(`role_id`, `menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '角色和菜单关联表';

-- ----------------------------
-- 6、系统管理表
-- ----------------------------
DROP TABLE IF EXISTS `meta_system`;
CREATE TABLE `meta_system` (
  `system_id`           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '系统ID',
  `system_name`         varchar(50)     NOT NULL                   COMMENT '系统名称',
  `system_code`         varchar(50)     NOT NULL                   COMMENT '系统编码',
  `database_type`       varchar(20)     NOT NULL                   COMMENT '数据库类型',
  `database_host`       varchar(100)    NOT NULL                   COMMENT '数据库主机',
  `database_port`       int(6)          NOT NULL                   COMMENT '数据库端口',
  `database_name`       varchar(50)     NOT NULL                   COMMENT '数据库名称',
  `database_username`   varchar(50)     NOT NULL                   COMMENT '数据库用户名',
  `database_password`   varchar(200)    NOT NULL                   COMMENT '数据库密码',
  `status`              char(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
  `create_by`           varchar(64)     DEFAULT ''                 COMMENT '创建者',
  `create_time`         datetime                                   COMMENT '创建时间',
  `update_by`           varchar(64)     DEFAULT ''                 COMMENT '更新者',
  `update_time`         datetime                                   COMMENT '更新时间',
  `remark`              varchar(500)    DEFAULT NULL               COMMENT '备注',
  `deleted`             char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`system_id`),
  UNIQUE KEY `uk_system_code` (`system_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT = '系统管理表';
