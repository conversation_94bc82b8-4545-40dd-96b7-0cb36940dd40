import { defineStore } from 'pinia'
import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  }),

  actions: {
    // 登录
    async login(userInfo) {
      const { username, password, code, uuid } = userInfo
      try {
        const response = await login({
          username: username.trim(),
          password: password,
          code: code,
          uuid: uuid
        })
        const { token } = response.data
        setToken(token)
        this.token = token
        return response
      } catch (error) {
        throw error
      }
    },

    // 获取用户信息
    async getInfo() {
      try {
        const response = await getInfo()
        const { user, roles, permissions } = response.data
        
        this.name = user.userName
        this.avatar = user.avatar
        this.roles = roles
        this.permissions = permissions
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 退出登录
    async logout() {
      try {
        await logout()
      } catch (error) {
        console.log('退出登录失败:', error)
      } finally {
        this.token = ''
        this.name = ''
        this.avatar = ''
        this.roles = []
        this.permissions = []
        removeToken()
      }
    },

    // 重置token
    resetToken() {
      this.token = ''
      this.name = ''
      this.avatar = ''
      this.roles = []
      this.permissions = []
      removeToken()
    }
  }
})
