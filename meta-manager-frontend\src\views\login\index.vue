<template>
  <div class="login-container">
    <div class="login-form">
      <div class="login-header">
        <h2>基础数据配置管理平台</h2>
        <p>MetaManager</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form-content"
        auto-complete="on"
        label-position="left"
      >
        <el-form-item prop="username">
          <el-input
            ref="username"
            v-model="loginForm.username"
            placeholder="用户名"
            name="username"
            type="text"
            tabindex="1"
            auto-complete="on"
            size="large"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            auto-complete="on"
            size="large"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
            <template #suffix>
              <el-icon class="show-pwd" @click="showPwd">
                <component :is="passwordType === 'password' ? 'View' : 'Hide'" />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input
            v-model="loginForm.code"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            size="large"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img" />
          </div>
        </el-form-item>

        <el-form-item style="width:100%;">
          <el-button
            :loading="loading"
            size="large"
            type="primary"
            style="width:100%;"
            @click.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { getCodeImg } from '@/api/login'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

const loginFormRef = ref()
const passwordType = ref('password')
const loading = ref(false)
const captchaEnabled = ref(true)
const codeUrl = ref('')

const loginForm = ref({
  username: 'admin',
  password: 'admin123',
  code: '',
  uuid: ''
})

const loginRules = {
  username: [
    { required: true, trigger: 'blur', message: '请输入您的账号' }
  ],
  password: [
    { required: true, trigger: 'blur', message: '请输入您的密码' }
  ],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
}

const showPwd = () => {
  if (passwordType.value === 'password') {
    passwordType.value = ''
  } else {
    passwordType.value = 'password'
  }
  nextTick(() => {
    refs.password.focus()
  })
}

const getCode = () => {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = 'data:image/gif;base64,' + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

const handleLogin = () => {
  loginFormRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      userStore.login(loginForm.value).then(() => {
        const query = route.query
        const redirect = query.redirect || '/'
        router.push({ path: redirect })
      }).catch(() => {
        loading.value = false
        if (captchaEnabled.value) {
          getCode()
        }
      })
    }
  })
}

onMounted(() => {
  getCode()
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-form {
  border-radius: 6px;
  background: #fff;
  width: 400px;
  padding: 40px 35px 35px 35px;
  box-shadow: 0 0 25px #cac6c6;

  .login-header {
    text-align: center;
    margin-bottom: 30px;
    
    h2 {
      color: #303133;
      margin-bottom: 10px;
      font-weight: 500;
    }
    
    p {
      color: #909399;
      font-size: 14px;
    }
  }

  .login-form-content {
    .el-input {
      height: 40px;
      
      input {
        height: 40px;
      }
    }

    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 0px;
    }
  }

  .login-code {
    width: 33%;
    height: 40px;
    float: right;
    
    img {
      cursor: pointer;
      vertical-align: middle;
      height: 40px;
      padding-left: 12px;
    }
  }

  .show-pwd {
    cursor: pointer;
    user-select: none;
  }
}
</style>
