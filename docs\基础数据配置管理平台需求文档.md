# 基础数据配置管理平台需求文档

## 1. 项目概述

### 1.1 背景
目前团队多个系统的基础数据和配置表维护需要通过开发人员编写SQL并提交OA申请执行，流程繁琐且效率低下。为提高工作效率，减少不必要的开发资源占用，拟开发一个基础数据配置管理平台，使产品经理能够直接维护基础数据。

### 1.2 目标
- 减少80%以上的基础数据维护相关OA申请
- 提高基础数据维护效率，从申请到执行的时间缩短至少75%
- 确保数据维护过程的安全性和可追溯性
- 降低开发人员在数据维护上的工作量

## 2. 功能需求

### 2.1 用户管理

#### 2.1.1 用户角色
- **系统管理员**：负责平台整体配置和用户权限管理
- **产品经理**：负责维护所属系统的基础数据
- **审核人员**：负责审核重要数据变更
- **查看人员**：仅有查看权限

#### 2.1.2 用户权限管理
- 支持用户创建、编辑、禁用和删除
- 支持角色分配和权限设置
- 支持按系统、数据表粒度分配权限
- 支持临时权限授予和回收

### 2.2 系统和数据表管理

#### 2.2.1 系统管理
- 支持添加、编辑和禁用系统
- 配置系统的数据库连接信息
- 设置系统级别的操作权限和审批流程

#### 2.2.2 数据表管理
- 支持添加、编辑和禁用可维护的数据表
- 配置表字段的显示名称、数据类型、是否必填等属性
- 设置字段级别的编辑权限
- 配置数据校验规则
- 支持表关联关系配置

### 2.3 数据维护功能

#### 2.3.1 数据查询
- 支持按条件筛选数据
- 支持排序和分页
- 支持导出查询结果
- 支持保存常用查询条件

#### 2.3.2 数据编辑
- 支持单条数据新增、编辑和删除
- 支持批量数据导入和导出
- 支持数据复制功能
- 提供数据编辑的表单验证
- 支持关联数据的级联操作

#### 2.3.3 批量操作
- 支持Excel导入数据
- 支持模板下载
- 支持数据校验和错误提示
- 支持批量更新和删除

### 2.4 审批流程

#### 2.4.1 审批配置
- 支持配置需要审批的数据表和操作类型
- 支持配置审批流程和审批人
- 支持设置审批时效

#### 2.4.2 审批流程
- 支持提交审批申请
- 支持审批通知和提醒
- 支持审批意见填写
- 支持审批历史查看
- 支持紧急审批通道

### 2.5 操作日志

#### 2.5.1 日志记录
- 记录所有数据变更操作，包括操作人、操作时间、操作类型、操作内容
- 记录数据变更前后的值
- 记录操作IP和设备信息

#### 2.5.2 日志查询
- 支持按时间、操作人、系统、表名等条件查询日志
- 支持导出日志
- 提供日志详情查看

### 2.6 版本控制

#### 2.6.1 变更历史
- 记录数据的所有变更历史
- 支持查看特定时间点的数据状态
- 支持数据对比功能

#### 2.6.2 回滚功能
- 支持将数据回滚到特定版本
- 记录回滚操作日志
- 支持回滚审批流程

## 3. 非功能需求

### 3.1 性能需求
- 页面加载时间不超过2秒
- 数据查询响应时间不超过3秒
- 支持同时100人以上在线操作
- 支持单表10万条以上数据的高效管理

### 3.2 安全需求
- 支持HTTPS加密传输
- 实现完善的权限控制机制
- 敏感数据加密存储
- 防SQL注入和XSS攻击
- 操作日志不可篡改

### 3.3 可用性需求
- 系统可用性达到99.9%
- 支持7*24小时访问
- 提供友好的错误提示
- 支持数据备份和恢复

### 3.4 兼容性需求
- 支持主流浏览器（Chrome、Firefox、Edge等）
- 支持响应式设计，适配PC和平板设备
- 支持多种数据库（MySQL、Oracle、SQL Server等）

## 4. 系统架构

### 4.1 技术架构
- 前端：React/Vue + Ant Design/Element UI
- 后端：Spring Boot + MyBatis
- 数据库：MySQL（主数据库）
- 缓存：Redis
- 消息队列：Kafka（用于日志记录和异步处理）

### 4.2 系统集成
- 支持与现有OA系统集成（用于特定场景的审批流转）
- 支持与用户中心集成（SSO单点登录）
- 支持与消息通知系统集成

### 4.3 部署架构
- 采用微服务架构
- 支持容器化部署
- 支持高可用集群部署

## 5. 数据模型

### 5.1 核心数据表
- 用户表
- 角色表
- 权限表
- 系统表
- 数据表配置表
- 字段配置表
- 操作日志表
- 审批流程表
- 审批记录表
- 数据变更历史表

### 5.2 关键字段说明
- 每张表必须包含：创建人、创建时间、修改人、修改时间
- 所有删除操作采用逻辑删除
- 敏感字段需加密存储

## 6. 用户界面

### 6.1 总体风格
- 简洁、直观的界面设计
- 一致的操作体验
- 适当的视觉反馈

### 6.2 主要界面
- 登录界面
- 系统选择界面
- 数据表列表界面
- 数据查询和编辑界面
- 批量导入界面
- 审批流程界面
- 操作日志界面
- 用户和权限管理界面



### 8.2 成功标准
- 基础数据维护相关的OA申请减少80%
- 产品经理对系统的满意度达到85%以上
- 系统稳定运行，无重大安全事故
- 数据维护效率提升75%以上

## 9. 附录

### 9.1 术语表
- **基础数据**：系统中相对稳定的、作为基础的数据，如字典表、配置表等
- **配置表**：用于系统配置的数据表，通常不包含业务数据
- **OA申请**：通过办公自动化系统提交的工作申请流程

