// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 通用类
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

// 布局相关
.container {
  padding: 20px;
}

.page-container {
  padding: 20px;
  background: #fff;
  margin: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

// 表格相关
.table-container {
  .el-table {
    border: 1px solid #ebeef5;
    
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 500;
      }
    }
  }
}

// 表单相关
.form-container {
  .el-form-item {
    margin-bottom: 22px;
  }
}

// 按钮相关
.btn-container {
  margin-bottom: 20px;
  
  .el-button {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}

// 卡片相关
.card-container {
  .el-card {
    margin-bottom: 20px;
    
    .el-card__header {
      padding: 18px 20px;
      border-bottom: 1px solid #ebeef5;
      
      .card-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
    
    .el-card__body {
      padding: 20px;
    }
  }
}

// 分页相关
.pagination-container {
  padding: 20px 0;
  text-align: right;
}

// 响应式
@media (max-width: 768px) {
  .page-container {
    margin: 10px;
    padding: 15px;
  }
  
  .btn-container {
    .el-button {
      margin-right: 5px;
      margin-bottom: 5px;
    }
  }
}
