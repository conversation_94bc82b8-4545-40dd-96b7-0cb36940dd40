# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api

# Spring配置
spring:
  application:
    name: meta-manager

  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ******************************************************************************************************************************************************
      username: agent_sales_test_user
      password: yKK@yeacL2Kqy1BD
      # 初始连接数
      initial-size: 10
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 200
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,*************
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  type-aliases-package: com.metamanager.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configuration:
    # 开启驼峰命名
    map-underscore-to-camel-case: true
    # 缓存数量
    cache-enabled: false
    # 懒加载
    lazy-loading-enabled: true
    # 多结果集
    multiple-result-sets-enabled: true
    # 使用列标签
    use-column-label: true
    # 使用生成的主键
    use-generated-keys: true
    # 自动映射
    auto-mapping-behavior: partial
    # 自动映射未知列
    auto-mapping-unknown-column-behavior: warning
    # 默认执行器
    default-executor-type: simple
    # 默认超时时间
    default-statement-timeout: 25000
    # 默认获取数量
    default-fetch-size: 100
    # 安全的行边界
    safe-row-bounds-enabled: false
    # 本地缓存机制
    local-cache-scope: session
    # 数据库超时时间
    jdbc-type-for-null: other
    # 懒加载触发方法
    lazy-load-trigger-methods: equals,clone,hashCode,toString
  global-config:
    db-config:
      # 主键类型
      id-type: ASSIGN_ID
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除全局值（1表示已删除）
      logic-delete-value: 1
      # 逻辑未删除全局值（0表示未删除）
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.metamanager: debug
    org.springframework: warn

# JWT配置
jwt:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥（至少32个字符，256位）
  secret: abcdefghijklmnopqrstuvwxyz123456
  # 令牌有效期（默认30分钟）
  expireTime: 30
