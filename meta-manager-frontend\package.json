{"name": "meta-manager-frontend", "version": "1.0.0", "description": "基础数据配置管理平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "axios": "^1.4.0", "@element-plus/icons-vue": "^2.1.0", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "@rushstack/eslint-patch": "^1.3.2", "sass": "^1.64.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1"}, "keywords": ["vue3", "element-plus", "vite", "meta-manager"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}