package com.metamanager.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.metamanager.common.Result;
import com.metamanager.entity.MetaSystem;
import com.metamanager.service.MetaSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * 系统管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/meta/system")
public class MetaSystemController {

    @Autowired
    private MetaSystemService metaSystemService;

    /**
     * 查询系统管理列表
     */
    @GetMapping("/list")
    public Result<IPage<MetaSystem>> list(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String systemName,
            @RequestParam(required = false) String systemCode,
            @RequestParam(required = false) String status) {

        Page<MetaSystem> page = new Page<>(pageNum, pageSize);
        QueryWrapper<MetaSystem> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(systemName)) {
            queryWrapper.like("system_name", systemName);
        }
        if (StringUtils.hasText(systemCode)) {
            queryWrapper.like("system_code", systemCode);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }

        queryWrapper.orderByDesc("create_time");
        IPage<MetaSystem> result = metaSystemService.page(page, queryWrapper);

        return Result.ok(result);
    }

    /**
     * 获取系统管理详细信息
     */
    @GetMapping("/{systemId}")
    public Result<MetaSystem> getInfo(@PathVariable("systemId") Long systemId) {
        MetaSystem metaSystem = metaSystemService.getById(systemId);
        return Result.ok(metaSystem);
    }

    /**
     * 新增系统管理
     */
    @PostMapping
    public Result<Void> add(@Valid @RequestBody MetaSystem metaSystem) {
        // 校验系统编码唯一性
        QueryWrapper<MetaSystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("system_code", metaSystem.getSystemCode());
        MetaSystem existSystem = metaSystemService.getOne(queryWrapper);
        if (existSystem != null) {
            return Result.fail("系统编码已存在");
        }

        boolean success = metaSystemService.save(metaSystem);
        return success ? Result.ok() : Result.fail();
    }

    /**
     * 修改系统管理
     */
    @PutMapping
    public Result<Void> edit(@Valid @RequestBody MetaSystem metaSystem) {
        // 校验系统编码唯一性
        QueryWrapper<MetaSystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("system_code", metaSystem.getSystemCode());
        queryWrapper.ne("system_id", metaSystem.getSystemId());
        MetaSystem existSystem = metaSystemService.getOne(queryWrapper);
        if (existSystem != null) {
            return Result.fail("系统编码已存在");
        }

        boolean success = metaSystemService.updateById(metaSystem);
        return success ? Result.ok() : Result.fail();
    }

    /**
     * 删除系统管理
     */
    @DeleteMapping("/{systemIds}")
    public Result<Void> remove(@PathVariable Long[] systemIds) {
        List<Long> idList = Arrays.asList(systemIds);
        boolean success = metaSystemService.removeByIds(idList);
        return success ? Result.ok() : Result.fail();
    }

    /**
     * 测试数据库连接
     */
    @PostMapping("/testConnection")
    public Result<Void> testConnection(@RequestBody MetaSystem metaSystem) {
        try {
            boolean success = metaSystemService.testConnection(metaSystem);
            if (success) {
                return Result.ok();
            } else {
                return Result.fail("连接测试失败");
            }
        } catch (Exception e) {
            return Result.fail("连接测试失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有可用系统列表
     */
    @GetMapping("/options")
    public Result<List<MetaSystem>> getSystemOptions() {
        QueryWrapper<MetaSystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "0");
        queryWrapper.select("system_id", "system_name", "system_code");
        queryWrapper.orderBy(true, true, "system_name");

        List<MetaSystem> systemList = metaSystemService.list(queryWrapper);
        return Result.ok(systemList);
    }
}
