# 基础数据配置管理平台 (MetaManager)

## 项目简介
基础数据配置管理平台，用于简化基础数据和配置表的维护工作，减少开发人员的工作量，提高数据维护效率。

## 技术栈
- **前端**: Vue 3 + Element Plus + Vite
- **后端**: Spring Boot + MyBatis-Plus + Spring Security
- **数据库**: MySQL
- **缓存**: Redis

## 项目结构
```
MetaManager/
├── meta-manager-backend/     # 后端项目
├── meta-manager-frontend/    # 前端项目
├── docs/                     # 文档
└── README.md
```

## 快速开始

### 后端启动
```bash
cd meta-manager-backend
mvn spring-boot:run
```

### 前端启动
```bash
cd meta-manager-frontend
npm install
npm run dev
```

## 功能特性
- 用户权限管理
- 系统和数据表管理
- 数据维护功能
- 审批流程
- 操作日志
- 版本控制

## 开发进度
- [x] 项目初始化
- [x] 后端基础框架
- [x] 前端基础框架
- [x] 用户认证模块（基础版本）
- [x] 基础页面结构
- [ ] 核心功能模块
- [ ] 高级功能模块

## 环境要求
- **Java**: JDK 8+
- **Node.js**: 16+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Redis**: 6.0+

## 安装部署

### 1. 数据库初始化
```sql
-- 执行数据库初始化脚本
source meta-manager-backend/src/main/resources/sql/meta_manager.sql
```

### 2. 后端启动
```bash
# 方式一：使用启动脚本（Windows）
start-backend.bat

# 方式二：手动启动
cd meta-manager-backend
mvn spring-boot:run
```

### 3. 前端启动
```bash
# 方式一：使用启动脚本（Windows）
start-frontend.bat

# 方式二：手动启动
cd meta-manager-frontend
npm install
npm run dev
```

### 4. 访问系统
- 前端地址：http://localhost:3000
- 后端地址：http://localhost:8080/api
- 默认账号：admin / admin123

## 已实现功能
- ✅ 用户登录/退出
- ✅ 主界面布局
- ✅ 路由导航
- ✅ 用户管理（基础CRUD界面）
- ✅ 系统管理（基础CRUD界面）
- ✅ 响应式设计

## 待开发功能
- 🔄 JWT认证完善
- 🔄 权限管理系统
- 🔄 数据表管理
- 🔄 数据维护功能
- 🔄 审批流程
- 🔄 操作日志
- 🔄 版本控制
