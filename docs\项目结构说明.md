# 项目结构说明

## 整体架构

基础数据配置管理平台采用前后端分离架构：
- **前端**: Vue 3 + Element Plus + Vite
- **后端**: Spring Boot + MyBatis-Plus + MySQL

## 目录结构

```
MetaManager/
├── docs/                                    # 文档目录
│   ├── 基础数据配置管理平台需求文档.md
│   └── 项目结构说明.md
├── meta-manager-backend/                    # 后端项目
│   ├── src/main/java/com/metamanager/
│   │   ├── MetaManagerApplication.java      # 启动类
│   │   ├── common/                          # 公共组件
│   │   │   ├── BaseEntity.java              # 基础实体类
│   │   │   ├── Constants.java               # 常量定义
│   │   │   └── Result.java                  # 统一返回结果
│   │   ├── config/                          # 配置类
│   │   │   ├── CorsConfig.java              # 跨域配置
│   │   │   └── SecurityConfig.java          # 安全配置
│   │   ├── controller/                      # 控制器层
│   │   │   └── LoginController.java         # 登录控制器
│   │   ├── dto/                             # 数据传输对象
│   │   │   ├── LoginRequest.java            # 登录请求
│   │   │   └── LoginResponse.java           # 登录响应
│   │   ├── entity/                          # 实体类
│   │   │   └── User.java                    # 用户实体
│   │   ├── service/                         # 服务层
│   │   │   ├── LoginService.java            # 登录服务接口
│   │   │   └── impl/
│   │   │       └── LoginServiceImpl.java    # 登录服务实现
│   │   └── mapper/                          # 数据访问层
│   ├── src/main/resources/
│   │   ├── application.yml                  # 应用配置
│   │   ├── mapper/                          # MyBatis映射文件
│   │   └── sql/
│   │       └── meta_manager.sql             # 数据库初始化脚本
│   └── pom.xml                              # Maven配置
├── meta-manager-frontend/                   # 前端项目
│   ├── src/
│   │   ├── api/                             # API接口
│   │   │   └── login.js                     # 登录API
│   │   ├── assets/                          # 静态资源
│   │   │   └── styles/
│   │   │       └── index.scss               # 全局样式
│   │   ├── components/                      # 公共组件
│   │   ├── layout/                          # 布局组件
│   │   │   ├── index.vue                    # 主布局
│   │   │   └── components/
│   │   │       ├── AppLink.vue              # 链接组件
│   │   │       ├── Hamburger.vue            # 汉堡菜单
│   │   │       └── SidebarItem.vue          # 侧边栏项
│   │   ├── router/                          # 路由配置
│   │   │   └── index.js                     # 路由定义
│   │   ├── stores/                          # 状态管理
│   │   │   └── user.js                      # 用户状态
│   │   ├── utils/                           # 工具函数
│   │   │   ├── auth.js                      # 认证工具
│   │   │   └── request.js                   # HTTP请求工具
│   │   ├── views/                           # 页面组件
│   │   │   ├── dashboard/                   # 首页
│   │   │   ├── error/                       # 错误页面
│   │   │   ├── login/                       # 登录页面
│   │   │   ├── system/                      # 系统管理
│   │   │   ├── meta/                        # 元数据管理
│   │   │   ├── approval/                    # 审批管理
│   │   │   └── log/                         # 日志管理
│   │   ├── App.vue                          # 根组件
│   │   └── main.js                          # 入口文件
│   ├── index.html                           # HTML模板
│   ├── package.json                         # 依赖配置
│   └── vite.config.js                       # Vite配置
├── start-backend.bat                        # 后端启动脚本
├── start-frontend.bat                       # 前端启动脚本
└── README.md                                # 项目说明
```

## 技术选型说明

### 后端技术栈
- **Spring Boot 2.7.14**: 主框架，提供自动配置和快速开发能力
- **MyBatis-Plus *********: ORM框架，简化数据库操作
- **Spring Security**: 安全框架，提供认证和授权
- **MySQL**: 关系型数据库
- **Redis**: 缓存数据库
- **Druid**: 数据库连接池
- **JWT**: 无状态认证
- **Swagger**: API文档生成

### 前端技术栈
- **Vue 3**: 渐进式JavaScript框架
- **Element Plus**: Vue 3 UI组件库
- **Vite**: 现代化构建工具
- **Vue Router**: 路由管理
- **Pinia**: 状态管理
- **Axios**: HTTP客户端
- **Sass**: CSS预处理器

## 核心功能模块

### 1. 用户认证模块
- 用户登录/退出
- JWT令牌管理
- 权限验证
- 路由守卫

### 2. 系统管理模块
- 用户管理
- 角色管理
- 菜单管理
- 权限分配

### 3. 元数据管理模块
- 系统配置管理
- 数据表管理
- 字段配置管理
- 数据维护功能

### 4. 审批流程模块
- 审批配置
- 审批流程管理
- 审批记录查询

### 5. 日志管理模块
- 操作日志记录
- 日志查询和导出
- 数据变更历史

## 开发规范

### 后端开发规范
1. **包结构**: 按功能模块划分包结构
2. **命名规范**: 使用驼峰命名法
3. **注释规范**: 类和方法必须添加注释
4. **异常处理**: 统一异常处理机制
5. **返回格式**: 统一使用Result包装返回结果

### 前端开发规范
1. **组件命名**: 使用PascalCase命名组件
2. **文件结构**: 按功能模块组织文件
3. **样式规范**: 使用Sass，遵循BEM命名规范
4. **API调用**: 统一使用封装的request工具
5. **状态管理**: 合理使用Pinia管理全局状态

## 部署说明

### 开发环境
1. 确保安装了Java 8+、Node.js 16+、Maven 3.6+
2. 启动MySQL和Redis服务
3. 执行数据库初始化脚本
4. 分别启动后端和前端项目

### 生产环境
1. 后端打包：`mvn clean package`
2. 前端打包：`npm run build`
3. 部署到服务器，配置Nginx反向代理
4. 配置生产环境数据库和Redis连接

## 后续开发计划

1. **完善认证授权**: 实现完整的JWT认证和权限控制
2. **核心功能开发**: 实现数据表管理和数据维护功能
3. **审批流程**: 实现可配置的审批流程
4. **日志系统**: 完善操作日志和审计功能
5. **性能优化**: 数据库优化、缓存策略、前端性能优化
6. **测试完善**: 单元测试、集成测试、E2E测试
