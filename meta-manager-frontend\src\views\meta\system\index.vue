<template>
  <div class="app-container">
    <div class="page-container">
      <div class="search-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true">
          <el-form-item label="系统名称" prop="systemName">
            <el-input
              v-model="queryParams.systemName"
              placeholder="请输入系统名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="系统编码" prop="systemCode">
            <el-input
              v-model="queryParams.systemCode"
              placeholder="请输入系统编码"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="系统状态" clearable>
              <el-option label="正常" value="0" />
              <el-option label="停用" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="btn-container">
        <el-button type="primary" icon="Plus" @click="handleAdd">新增系统</el-button>
        <el-button type="success" icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        <el-button type="danger" icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        <el-button type="info" icon="Connection" @click="handleTestConnection">测试连接</el-button>
      </div>

      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="systemList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="系统ID" align="center" prop="systemId" width="80" />
          <el-table-column label="系统名称" align="center" prop="systemName" :show-overflow-tooltip="true" />
          <el-table-column label="系统编码" align="center" prop="systemCode" />
          <el-table-column label="数据库类型" align="center" prop="databaseType" width="100" />
          <el-table-column label="数据库主机" align="center" prop="databaseHost" :show-overflow-tooltip="true" />
          <el-table-column label="数据库名称" align="center" prop="databaseName" />
          <el-table-column label="状态" align="center" key="status" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                {{ scope.row.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="160">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-container">
        <el-pagination
          v-show="total > 0"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加或修改系统配置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="systemRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="form.systemName" placeholder="请输入系统名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="系统编码" prop="systemCode">
              <el-input v-model="form.systemCode" placeholder="请输入系统编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据库类型" prop="databaseType">
              <el-select v-model="form.databaseType" placeholder="请选择数据库类型">
                <el-option label="MySQL" value="mysql" />
                <el-option label="Oracle" value="oracle" />
                <el-option label="SQL Server" value="sqlserver" />
                <el-option label="PostgreSQL" value="postgresql" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库主机" prop="databaseHost">
              <el-input v-model="form.databaseHost" placeholder="请输入数据库主机地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据库端口" prop="databasePort">
              <el-input-number v-model="form.databasePort" :min="1" :max="65535" placeholder="请输入端口号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库名称" prop="databaseName">
              <el-input v-model="form.databaseName" placeholder="请输入数据库名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名" prop="databaseUsername">
              <el-input v-model="form.databaseUsername" placeholder="请输入数据库用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="databasePassword">
              <el-input v-model="form.databasePassword" type="password" placeholder="请输入数据库密码" show-password />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
          <el-button type="info" @click="testConnection">测试连接</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
// 模拟数据和方法
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const open = ref(false)
const systemList = ref([])

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  systemName: undefined,
  systemCode: undefined,
  status: undefined
})

const form = ref({})
const rules = {
  systemName: [{ required: true, message: "系统名称不能为空", trigger: "blur" }],
  systemCode: [{ required: true, message: "系统编码不能为空", trigger: "blur" }],
  databaseType: [{ required: true, message: "数据库类型不能为空", trigger: "change" }],
  databaseHost: [{ required: true, message: "数据库主机不能为空", trigger: "blur" }],
  databasePort: [{ required: true, message: "数据库端口不能为空", trigger: "blur" }],
  databaseName: [{ required: true, message: "数据库名称不能为空", trigger: "blur" }],
  databaseUsername: [{ required: true, message: "数据库用户名不能为空", trigger: "blur" }],
  databasePassword: [{ required: true, message: "数据库密码不能为空", trigger: "blur" }]
}

// 模拟系统数据
const mockSystemList = [
  {
    systemId: 1,
    systemName: '用户管理系统',
    systemCode: 'USER_SYSTEM',
    databaseType: 'mysql',
    databaseHost: 'localhost',
    databasePort: 3306,
    databaseName: 'user_db',
    databaseUsername: 'root',
    status: '0',
    createTime: '2023-01-01 00:00:00'
  },
  {
    systemId: 2,
    systemName: '订单管理系统',
    systemCode: 'ORDER_SYSTEM',
    databaseType: 'mysql',
    databaseHost: 'localhost',
    databasePort: 3306,
    databaseName: 'order_db',
    databaseUsername: 'root',
    status: '0',
    createTime: '2023-01-02 00:00:00'
  }
]

// 获取系统列表
const getList = () => {
  loading.value = true
  setTimeout(() => {
    systemList.value = mockSystemList
    total.value = mockSystemList.length
    loading.value = false
  }, 500)
}

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    systemName: undefined,
    systemCode: undefined,
    status: undefined
  }
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.systemId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  open.value = true
  title.value = "添加系统"
}

// 查看按钮操作
const handleView = (row) => {
  reset()
  form.value = { ...row }
  open.value = true
  title.value = "查看系统"
}

// 修改按钮操作
const handleUpdate = (row) => {
  reset()
  form.value = { ...row }
  open.value = true
  title.value = "修改系统"
}

// 删除按钮操作
const handleDelete = (row) => {
  const systemIds = row.systemId || ids.value
  ElMessageBox.confirm('是否确认删除系统编号为"' + systemIds + '"的数据项？', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    ElMessage.success("删除成功")
    getList()
  })
}

// 测试连接
const handleTestConnection = () => {
  if (ids.value.length === 0) {
    ElMessage.warning("请选择要测试的系统")
    return
  }
  ElMessage.success("连接测试成功")
}

const testConnection = () => {
  ElMessage.success("数据库连接测试成功")
}

// 表单重置
const reset = () => {
  form.value = {
    systemId: undefined,
    systemName: undefined,
    systemCode: undefined,
    databaseType: 'mysql',
    databaseHost: undefined,
    databasePort: 3306,
    databaseName: undefined,
    databaseUsername: undefined,
    databasePassword: undefined,
    status: "0",
    remark: undefined
  }
}

// 取消按钮
const cancel = () => {
  open.value = false
  reset()
}

// 提交表单
const submitForm = () => {
  ElMessage.success("保存成功")
  open.value = false
  getList()
}

// 分页
const handleSizeChange = (val) => {
  queryParams.value.pageSize = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val
  getList()
}

// 时间格式化
const parseTime = (time) => {
  if (!time) return ''
  return time
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  .search-container {
    background: #fff;
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 4px;
  }
}
</style>
