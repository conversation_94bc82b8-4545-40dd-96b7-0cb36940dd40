<template>
  <div class="app-container">
    <div class="page-container">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>数据表管理</span>
          </div>
        </template>
        <div class="coming-soon">
          <el-icon size="80" color="#67C23A">
            <Grid />
          </el-icon>
          <h2>数据表管理功能</h2>
          <p>该功能正在开发中，敬请期待...</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
// 数据表管理页面
</script>

<style lang="scss" scoped>
.coming-soon {
  text-align: center;
  padding: 60px 20px;
  
  h2 {
    margin: 20px 0 10px;
    color: #303133;
  }
  
  p {
    color: #909399;
    font-size: 16px;
  }
}
</style>
