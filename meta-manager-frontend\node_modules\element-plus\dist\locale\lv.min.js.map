{"version": 3, "file": "lv.min.js", "sources": ["../../../../packages/locale/lang/lv.ts"], "sourcesContent": ["export default {\n  name: 'lv',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON>ad',\n      today: '<PERSON><PERSON><PERSON>',\n      cancel: 'Atcelt',\n      clear: 'Not<PERSON><PERSON><PERSON><PERSON>',\n      confirm: '<PERSON>i',\n      selectDate: 'Izvēlēties datumu',\n      selectTime: 'Izvēlēties laiku',\n      startDate: 'Sākuma datums',\n      startTime: 'Sākuma laiks',\n      endDate: 'Beigu datums',\n      endTime: 'Beigu laiks',\n      prevYear: 'Iepriek<PERSON>ējais gads',\n      nextYear: 'Nāka<PERSON>is gads',\n      prevMonth: 'I<PERSON>riek<PERSON>ē<PERSON><PERSON> mēnesis',\n      nextMonth: 'Nāka<PERSON>is mēnesis',\n      year: '',\n      month1: 'Janvāris',\n      month2: 'Febru<PERSON>ris',\n      month3: 'Marts',\n      month4: 'Aprī<PERSON>',\n      month5: 'Maijs',\n      month6: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      month7: 'J<PERSON>lijs',\n      month8: 'Augusts',\n      month9: 'Septembris',\n      month10: 'Oktobris',\n      month11: 'Novembris',\n      month12: 'Decembris',\n      // week: 'nedēļa',\n      weeks: {\n        sun: 'Sv',\n        mon: 'Pr',\n        tue: 'Ot',\n        wed: 'Tr',\n        thu: 'Ce',\n        fri: 'Pk',\n        sat: 'Se',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mai',\n        jun: 'Jūn',\n        jul: 'Jūl',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Ielādē',\n      noMatch: 'Nav atbilstošu datu',\n      noData: 'Nav datu',\n      placeholder: 'Izvēlēties',\n    },\n    mention: {\n      loading: 'Ielādē',\n    },\n    cascader: {\n      noMatch: 'Nav atbilstošu datu',\n      loading: 'Ielādē',\n      placeholder: 'Izvēlēties',\n      noData: 'Nav datu',\n    },\n    pagination: {\n      goto: 'Iet uz',\n      pagesize: '/lapa',\n      total: 'Kopā {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Paziņojums',\n      confirm: 'Labi',\n      cancel: 'Atcelt',\n      error: 'Nederīga ievade',\n    },\n    upload: {\n      deleteTip: 'Nospiediet dzēst lai izņemtu',\n      delete: 'Dzēst',\n      preview: 'Priekšskatīt',\n      continue: 'Turpināt',\n    },\n    table: {\n      emptyText: 'Nav datu',\n      confirmFilter: 'Apstiprināt',\n      resetFilter: 'Atiestatīt',\n      clearFilter: 'Visi',\n      sumText: 'Summa',\n    },\n    tree: {\n      emptyText: 'Nav datu',\n    },\n    transfer: {\n      noMatch: 'Nav atbilstošu datu',\n      noData: 'Nav datu',\n      titles: ['Saraksts 1', 'Saraksts 2'],\n      filterPlaceholder: 'Ievadīt atslēgvārdu',\n      noCheckedFormat: '{total} vienības',\n      hasCheckedFormat: '{checked}/{total} atzīmēti',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,6BAA6B,CAAC,UAAU,CAAC,4BAA4B,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,8BAA8B,CAAC,QAAQ,CAAC,oBAAoB,CAAC,SAAS,CAAC,sCAAsC,CAAC,SAAS,CAAC,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,sBAAsB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,wBAAwB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,CAAC,WAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,oCAAoC,CAAC,eAAe,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,sCAAsC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}